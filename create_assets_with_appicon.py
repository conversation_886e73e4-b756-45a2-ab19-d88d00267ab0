#!/usr/bin/env python3
import os
import json
import shutil
import subprocess
import glob

def create_asset_catalog_with_appicon():
    """创建包含AppIcon的Asset Catalog"""
    catalog_name = "ImagesWithAppIcon.xcassets"
    
    # 如果目录已存在，先删除
    if os.path.exists(catalog_name):
        shutil.rmtree(catalog_name)
    
    # 创建主目录
    os.makedirs(catalog_name)
    
    # 创建Contents.json
    contents_json = {
        "info": {
            "author": "xcode",
            "version": 1
        }
    }
    
    with open(os.path.join(catalog_name, "Contents.json"), "w") as f:
        json.dump(contents_json, f, indent=2)
    
    # 复制AppIcon.appiconset到新的catalog中
    if os.path.exists("AppIcon.appiconset"):
        shutil.copytree("AppIcon.appiconset", os.path.join(catalog_name, "AppIcon.appiconset"))
        print("✅ AppIcon.appiconset 已添加到Asset Catalog")
    else:
        print("❌ 找不到AppIcon.appiconset文件夹")
        return None
    
    return catalog_name

def process_images(catalog_path):
    """处理所有PNG图片文件（排除AppIcon相关文件）"""
    png_files = glob.glob("*.png")
    
    # 排除AppIcon相关的文件
    excluded_files = ["<EMAIL>"]  # 这个已经用来生成AppIcon了
    
    for png_file in png_files:
        if png_file in excluded_files:
            continue
            
        # 清理文件名，移除特殊字符和空格
        base_name = os.path.splitext(png_file)[0]
        # 移除_Normal后缀和分辨率标识
        clean_name = base_name.replace("_Normal", "").replace(" ", "_")
        
        # 检测分辨率
        scale = "1x"
        if "@2x" in base_name:
            scale = "2x"
            clean_name = clean_name.replace("@2x", "")
        elif "@3x" in base_name:
            scale = "3x"
            clean_name = clean_name.replace("@3x", "")
        
        # 创建imageset目录
        imageset_name = f"{clean_name}.imageset"
        imageset_path = os.path.join(catalog_path, imageset_name)
        
        if not os.path.exists(imageset_path):
            os.makedirs(imageset_path)
            
            # 创建Contents.json
            imageset_contents = {
                "images": [
                    {
                        "filename": f"{clean_name}.png",
                        "idiom": "universal",
                        "scale": "1x"
                    },
                    {
                        "idiom": "universal",
                        "scale": "2x"
                    },
                    {
                        "idiom": "universal",
                        "scale": "3x"
                    }
                ],
                "info": {
                    "author": "xcode",
                    "version": 1
                }
            }
            
            with open(os.path.join(imageset_path, "Contents.json"), "w") as f:
                json.dump(imageset_contents, f, indent=2)
        
        # 复制图片文件
        target_filename = f"{clean_name}.png"
        if scale == "2x":
            target_filename = f"{clean_name}@2x.png"
        elif scale == "3x":
            target_filename = f"{clean_name}@3x.png"
            
        shutil.copy2(png_file, os.path.join(imageset_path, target_filename))
        
        # 更新Contents.json以包含正确的文件名
        contents_path = os.path.join(imageset_path, "Contents.json")
        with open(contents_path, "r") as f:
            contents = json.load(f)
        
        # 更新对应scale的filename
        for image in contents["images"]:
            if image["scale"] == scale:
                image["filename"] = target_filename
        
        with open(contents_path, "w") as f:
            json.dump(contents, f, indent=2)

def compile_assets_car(catalog_path):
    """使用actool编译Assets.car"""
    output_dir = "CompiledAssetsWithAppIcon"
    if os.path.exists(output_dir):
        shutil.rmtree(output_dir)
    os.makedirs(output_dir)
    
    cmd = [
        "xcrun", "actool",
        catalog_path,
        "--compile", output_dir,
        "--platform", "iphoneos",
        "--minimum-deployment-target", "12.0",
        "--app-icon", "AppIcon",
        "--output-partial-info-plist", os.path.join(output_dir, "partial-info.plist")
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        print("✅ 编译成功!")
        print(f"Assets.car文件位置: {os.path.join(output_dir, 'Assets.car')}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 编译失败: {e}")
        print(f"标准输出: {e.stdout}")
        print(f"错误输出: {e.stderr}")
        return False

def main():
    print("🚀 开始创建包含AppIcon的Asset Catalog...")
    
    # 检查AppIcon.appiconset是否存在
    if not os.path.exists("AppIcon.appiconset"):
        print("❌ 找不到AppIcon.appiconset，请先运行create_app_icon.py")
        return
    
    catalog_path = create_asset_catalog_with_appicon()
    if not catalog_path:
        return
    
    print("📸 处理图片文件...")
    process_images(catalog_path)
    
    print("🔨 编译Assets.car...")
    success = compile_assets_car(catalog_path)
    
    if success:
        print("🎉 Assets.car创建完成!")
        # 将Assets.car移动到当前目录
        source = os.path.join("CompiledAssetsWithAppIcon", "Assets.car")
        target = "AssetsWithAppIcon.car"
        if os.path.exists(source):
            shutil.copy2(source, target)
            print(f"📦 {target} 已复制到当前目录")
            
            # 显示文件大小
            size = os.path.getsize(target)
            print(f"📊 文件大小: {size / (1024*1024):.1f}MB")
    else:
        print("❌ 创建失败，请检查错误信息")

if __name__ == "__main__":
    main()
