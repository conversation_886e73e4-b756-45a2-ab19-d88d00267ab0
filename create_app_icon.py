#!/usr/bin/env python3
import os
import json
import shutil
import subprocess
from PIL import Image

def get_image_size(image_path):
    """获取图片尺寸"""
    with Image.open(image_path) as img:
        return img.size

def resize_image(input_path, output_path, size):
    """调整图片尺寸"""
    with Image.open(input_path) as img:
        # 使用高质量重采样
        resized_img = img.resize((size, size), Image.Resampling.LANCZOS)
        resized_img.save(output_path, "PNG", optimize=True)

def create_app_icon_set():
    """创建AppIcon.appiconset"""
    source_image = "<EMAIL>"
    
    if not os.path.exists(source_image):
        print(f"错误: 找不到源图片 {source_image}")
        return False
    
    # 检查源图片尺寸
    width, height = get_image_size(source_image)
    print(f"源图片尺寸: {width}x{height}")
    
    # 创建AppIcon.appiconset目录
    appiconset_dir = "AppIcon.appiconset"
    if os.path.exists(appiconset_dir):
        shutil.rmtree(appiconset_dir)
    os.makedirs(appiconset_dir)
    
    # AppIcon所需的所有尺寸
    icon_sizes = [
        # iPhone
        {"size": "20x20", "scale": "2x", "filename": "<EMAIL>", "pixel_size": 40},
        {"size": "20x20", "scale": "3x", "filename": "<EMAIL>", "pixel_size": 60},
        {"size": "29x29", "scale": "2x", "filename": "<EMAIL>", "pixel_size": 58},
        {"size": "29x29", "scale": "3x", "filename": "<EMAIL>", "pixel_size": 87},
        {"size": "40x40", "scale": "2x", "filename": "<EMAIL>", "pixel_size": 80},
        {"size": "40x40", "scale": "3x", "filename": "<EMAIL>", "pixel_size": 120},
        {"size": "60x60", "scale": "2x", "filename": "<EMAIL>", "pixel_size": 120},
        {"size": "60x60", "scale": "3x", "filename": "<EMAIL>", "pixel_size": 180},
        
        # iPad
        {"size": "20x20", "scale": "1x", "filename": "Icon-20.png", "pixel_size": 20},
        {"size": "29x29", "scale": "1x", "filename": "Icon-29.png", "pixel_size": 29},
        {"size": "40x40", "scale": "1x", "filename": "Icon-40.png", "pixel_size": 40},
        {"size": "76x76", "scale": "1x", "filename": "Icon-76.png", "pixel_size": 76},
        {"size": "76x76", "scale": "2x", "filename": "<EMAIL>", "pixel_size": 152},
        {"size": "83.5x83.5", "scale": "2x", "filename": "<EMAIL>", "pixel_size": 167},
        
        # App Store
        {"size": "1024x1024", "scale": "1x", "filename": "Icon-1024.png", "pixel_size": 1024},
    ]
    
    # 生成所有尺寸的图标
    for icon in icon_sizes:
        output_path = os.path.join(appiconset_dir, icon["filename"])
        resize_image(source_image, output_path, icon["pixel_size"])
        print(f"生成: {icon['filename']} ({icon['pixel_size']}x{icon['pixel_size']})")
    
    # 创建Contents.json
    contents_json = {
        "images": [
            # iPhone
            {
                "filename": "<EMAIL>",
                "idiom": "iphone",
                "scale": "2x",
                "size": "20x20"
            },
            {
                "filename": "<EMAIL>",
                "idiom": "iphone",
                "scale": "3x",
                "size": "20x20"
            },
            {
                "filename": "<EMAIL>",
                "idiom": "iphone",
                "scale": "2x",
                "size": "29x29"
            },
            {
                "filename": "<EMAIL>",
                "idiom": "iphone",
                "scale": "3x",
                "size": "29x29"
            },
            {
                "filename": "<EMAIL>",
                "idiom": "iphone",
                "scale": "2x",
                "size": "40x40"
            },
            {
                "filename": "<EMAIL>",
                "idiom": "iphone",
                "scale": "3x",
                "size": "40x40"
            },
            {
                "filename": "<EMAIL>",
                "idiom": "iphone",
                "scale": "2x",
                "size": "60x60"
            },
            {
                "filename": "<EMAIL>",
                "idiom": "iphone",
                "scale": "3x",
                "size": "60x60"
            },
            # iPad
            {
                "filename": "Icon-20.png",
                "idiom": "ipad",
                "scale": "1x",
                "size": "20x20"
            },
            {
                "filename": "<EMAIL>",
                "idiom": "ipad",
                "scale": "2x",
                "size": "20x20"
            },
            {
                "filename": "Icon-29.png",
                "idiom": "ipad",
                "scale": "1x",
                "size": "29x29"
            },
            {
                "filename": "<EMAIL>",
                "idiom": "ipad",
                "scale": "2x",
                "size": "29x29"
            },
            {
                "filename": "Icon-40.png",
                "idiom": "ipad",
                "scale": "1x",
                "size": "40x40"
            },
            {
                "filename": "<EMAIL>",
                "idiom": "ipad",
                "scale": "2x",
                "size": "40x40"
            },
            {
                "filename": "Icon-76.png",
                "idiom": "ipad",
                "scale": "1x",
                "size": "76x76"
            },
            {
                "filename": "<EMAIL>",
                "idiom": "ipad",
                "scale": "2x",
                "size": "76x76"
            },
            {
                "filename": "<EMAIL>",
                "idiom": "ipad",
                "scale": "2x",
                "size": "83.5x83.5"
            },
            # App Store
            {
                "filename": "Icon-1024.png",
                "idiom": "ios-marketing",
                "scale": "1x",
                "size": "1024x1024"
            }
        ],
        "info": {
            "author": "xcode",
            "version": 1
        }
    }
    
    with open(os.path.join(appiconset_dir, "Contents.json"), "w") as f:
        json.dump(contents_json, f, indent=2)
    
    print(f"\nAppIcon.appiconset 创建完成!")
    print(f"包含 {len(icon_sizes)} 个不同尺寸的图标")
    return True

def main():
    print("开始创建AppIcon...")
    
    # 检查是否安装了PIL
    try:
        from PIL import Image
    except ImportError:
        print("错误: 需要安装Pillow库")
        print("请运行: pip install Pillow")
        return
    
    success = create_app_icon_set()
    
    if success:
        print("\n✅ AppIcon创建成功!")
        print("📁 生成的文件夹: AppIcon.appiconset")
        print("🎯 可以直接拖拽到Xcode项目中使用")
    else:
        print("❌ 创建失败")

if __name__ == "__main__":
    main()
